<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Customers extends \Theme25\ControllerSubMethods {

    /**
     * Унифицирано търсене в клиенти с кеширане
     */
    public function search($query, $page = 1, $limit = 20, $extendedSearch = 0) {
        // Проверяваме кеша първо
        $cacheKey = $this->modelSearch->generateCacheKey('customers_search', $query . '_ext_' . $extendedSearch . '_p' . $page . '_l' . $limit, $limit);
        $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;

        // Получаваме общия брой резултати
        $total = $this->getTotalCount($query, $extendedSearch);
        if ($total == 0) {
            return [
                'results' => [],
                'total' => 0
            ];
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, $limit, $extendedSearch, $offset);

        $searchResults = [
            'results' => $results,
            'total' => $total
        ];

        // Кешираме резултатите
        $this->modelSearch->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }


    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $extendedSearch = 0, $offset = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);

            // При обикновено търсене (extendedSearch = 0) връщаме само точните съвпадения
            if (!$extendedSearch) {
                return array_slice($exactResults, $offset, $limit);
            }

            // При разширено търсене (extendedSearch = 1) добавяме и частични съвпадения
            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търси точни съвпадения в клиенти с интелигентна логика
     */
    private function searchExactMatches($words, $limit) {
        $results = [];
        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е email адрес (точно съвпадение)
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    'exact' as match_type,
                    (SELECT COUNT(*) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as order_count,
                    (SELECT COALESCE(SUM(o.total), 0) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as total_spent
                FROM
                    " . DB_PREFIX . "customer c
                LEFT JOIN
                    " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE
                    LOWER(c.email) = '" . $escapedEmail . "'
                    AND cg.language_id = '" . (int)$language_id . "'
                    AND (c.firstname IS NOT NULL AND c.firstname != ''
                         OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY
                    c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_count' => $row['order_count'] ?? 0,
                    'total_spent' => $row['total_spent'] ?? 0,
                    'relevance' => 100
                ];
            }


            F()->log->developer('searchExactMatches 1', __FILE__, __LINE__);

            // Ако намерим точно съвпадение по email, връщаме САМО този клиент
            return $results;
        }

        // Проверяваме дали търсенето е част от email адрес (частично съвпадение)
        // Изключваме случаите, когато търсенето е валиден имейл адрес (те се обработват по-горе)
        if (count($words) == 1 && !filter_var($words[0], FILTER_VALIDATE_EMAIL) && (strpos($words[0], '@') !== false || strpos($words[0], '.') !== false || strlen($words[0]) >= 3)) {
            $escapedEmailPart = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    'email_partial' as match_type,
                    (SELECT COUNT(*) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as order_count,
                    (SELECT COALESCE(SUM(o.total), 0) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as total_spent
                FROM
                    " . DB_PREFIX . "customer c
                LEFT JOIN
                    " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE
                    LOWER(c.email) LIKE '%" . $escapedEmailPart . "%'
                    AND cg.language_id = '" . (int)$language_id . "'
                    AND (c.firstname IS NOT NULL AND c.firstname != ''
                         OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY
                    c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_count' => $row['order_count'] ?? 0,
                    'total_spent' => $row['total_spent'] ?? 0,
                    'relevance' => 90 // Малко по-ниска релевантност от точното съвпадение
                ];
            }

            F()->log->developer('searchExactMatches 2', __FILE__, __LINE__);

            // Ако намерим частично съвпадение по email, връщаме резултатите
            if (!empty($results)) {
                return $results;
            }
        }

        // Проверяваме дали търсенето е число (за order_id търсене)
        if (count($words) == 1 && is_numeric($words[0])) {
            $orderId = (int)$words[0];

            // Търсим клиента който е направил поръчка с този номер
            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    o.order_id,
                    'exact' as match_type,
                    (SELECT COUNT(*) FROM " . DB_PREFIX . "order o2 WHERE o2.email = c.email) as order_count,
                    (SELECT COALESCE(SUM(o2.total), 0) FROM " . DB_PREFIX . "order o2 WHERE o2.email = c.email) as total_spent
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                INNER JOIN " . DB_PREFIX . "order o ON (c.email = o.email)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND o.order_id = '" . (int)$orderId . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'status_class' => $this->getCustomerStatusClass($row['status']),
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_count' => (int)$row['order_count'],
                    'total_spent' => $this->formatCurrency($row['total_spent'], 'BGN', 1),
                    'relevance' => 100,
                    'found_via_order' => $row['order_id'] // Добавяме информация че е намерен чрез поръчка
                ];
            }

            F()->log->developer('searchExactMatches 3', __FILE__, __LINE__);

            // Ако намерим клиент чрез поръчка, връщаме резултата
            if (!empty($results)) {
                return $results;
            }
        }

        // Проверяваме дали търсенето е телефонен номер (само числа или с +)
        if (count($words) == 1 && preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $words[0])) {
            $phone = preg_replace('/[^\+0-9]/', '', $words[0]); // Премахваме всички символи освен + и цифри
            $escapedPhone = $this->db->escape($phone);

            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    'exact' as match_type,
                    (SELECT COUNT(*) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as order_count,
                    (SELECT COALESCE(SUM(o.total), 0) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as total_spent
                FROM
                    " . DB_PREFIX . "customer c
                LEFT JOIN
                    " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE
                    REPLACE(REPLACE(REPLACE(REPLACE(c.telephone, ' ', ''), '-', ''), '(', ''), ')', '') = '" . $escapedPhone . "'
                    AND cg.language_id = '" . (int)$language_id . "'
                    AND (c.firstname IS NOT NULL AND c.firstname != ''
                         OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY
                    c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'status_class' => $this->getCustomerStatusClass($row['status']),
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_count' => (int)$row['order_count'],
                    'total_spent' => $this->formatCurrency($row['total_spent'], 'BGN', 1),
                    'relevance' => 100
                ];
            }

            F()->log->developer('searchExactMatches 4', __FILE__, __LINE__);

            // Ако намерим точно съвпадение по телефон, връщаме САМО този клиент
            return $results;
        }

        return $results;
    }

    /**
     * Търси частични съвпадения в клиенти
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $results = [];
        $excludeIds = array_column($excludeResults, 'customer_id');
        $language_id = $this->getLanguageId();

        F()->log->developer('searchPartialMatches 1', __FILE__, __LINE__);

        // Проверяваме дали търсенето е число (за order_id търсене) - само ако няма точни резултати
        if (count($words) == 1 && is_numeric($words[0]) && empty($excludeResults)) {
            $orderId = (int)$words[0];

            // Търсим клиента който е направил поръчка с този номер
            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    o.order_id,
                    'partial' as match_type,
                    (SELECT COUNT(*) FROM " . DB_PREFIX . "order o2 WHERE o2.email = c.email) as order_count,
                    (SELECT COALESCE(SUM(o2.total), 0) FROM " . DB_PREFIX . "order o2 WHERE o2.email = c.email) as total_spent
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                INNER JOIN " . DB_PREFIX . "order o ON (c.email = o.email)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND o.order_id = '" . (int)$orderId . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'status_class' => $this->getCustomerStatusClass($row['status']),
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_count' => (int)$row['order_count'],
                    'total_spent' => $this->formatCurrency($row['total_spent'], 'BGN', 1),
                    'relevance' => 50, // По-ниска релевантност за частични резултати
                    'found_via_order' => $row['order_id']
                ];
            }

            F()->log->developer('searchPartialMatches 2', __FILE__, __LINE__);

            // Ако намерим клиент чрез поръчка, връщаме резултата
            if (!empty($results)) {
                return $results;
            }
        }

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(c.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(c.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(c.email) LIKE '%{$escapedWord}%'",
                "LOWER(c.telephone) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(c.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените клиенти
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND c.customer_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                c.customer_id,
                c.firstname,
                c.lastname,
                c.email,
                c.telephone,
                c.date_added,
                c.status,
                cg.name as customer_group,
                'partial' as match_type,
                (SELECT COUNT(*) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as order_count,
                (SELECT COALESCE(SUM(o.total), 0) FROM " . DB_PREFIX . "order o WHERE o.email = c.email) as total_spent
            FROM
                " . DB_PREFIX . "customer c
            LEFT JOIN
                " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
            WHERE
                cg.language_id = '" . (int)$language_id . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                AND ({$whereCondition})
            ORDER BY
                c.date_added DESC
            LIMIT " . (int)$limit;

            

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $name = trim($row['firstname'] . ' ' . $row['lastname']);
            // Пропускаме записи с празни имена
            if (empty($name) || $name === ' ') {
                continue;
            }

            $results[] = [
                'customer_id' => $row['customer_id'],
                'name' => $name,
                'email' => $row['email'] ?? '',
                'telephone' => $row['telephone'] ?? '',
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'customer_group' => $row['customer_group'] ?? '',
                'status' => $row['status'] ? 'Активен' : 'Неактивен',
                'status_class' => $this->getCustomerStatusClass($row['status']),
                'order_count' => (int)$row['order_count'],
                'total_spent' => $this->formatCurrency($row['total_spent'], 'BGN', 1),
                'relevance' => 50
            ];
        }

        F()->log->developer('searchPartialMatches 3', __FILE__, __LINE__);

        return $results;
    }




    /**
     * Получава общия брой резултати за дадена заявка
     * Използва същата логика като performOptimizedSearch() за консистентност
     */
    private function getTotalCount($query, $extendedSearch = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return 0;
            }

            // Броим точните съвпадения
            $exactCount = $this->getExactMatchesCount($words);

            // При обикновено търсене (extendedSearch = 0) връщаме само точните съвпадения
            if (!$extendedSearch) {
                return $exactCount;
            }

            // При разширено търсене (extendedSearch = 1) добавяме и частичните съвпадения
            // Броим частичните съвпадения (без дублиране с точните)
            $partialCount = $this->getPartialMatchesCount($words, $exactCount > 0);

            return $exactCount + $partialCount;

        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Брои точните съвпадения
     */
    private function getExactMatchesCount($words) {
        $totalCount = 0;
        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е email адрес (точно съвпадение)
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT COUNT(DISTINCT c.customer_id) as total
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND LOWER(c.email) = '{$escapedEmail}'";

            $result = $this->db->query($sql);
            $totalCount += (int)$result->row['total'];
        }

        // Проверяваме дали търсенето е част от email адрес (частично съвпадение)
        // Изключваме случаите, когато търсенето е валиден имейл адрес (те се обработват по-горе)
        if (count($words) == 1 && !filter_var($words[0], FILTER_VALIDATE_EMAIL) && (strpos($words[0], '@') !== false || strpos($words[0], '.') !== false || strlen($words[0]) >= 3)) {
            $escapedEmailPart = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT COUNT(DISTINCT c.customer_id) as total
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND LOWER(c.email) LIKE '%{$escapedEmailPart}%'";

            $result = $this->db->query($sql);
            $totalCount += (int)$result->row['total'];
        }

        // Проверяваме дали търсенето е телефонен номер
        if (count($words) == 1 && preg_match('/^[\d\s\+\-\(\)]+$/', $words[0])) {
            $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $words[0]);
            $escapedPhone = $this->db->escape($cleanPhone);

            $sql = "
                SELECT COUNT(DISTINCT c.customer_id) as total
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND REPLACE(REPLACE(REPLACE(REPLACE(c.telephone, ' ', ''), '-', ''), '(', ''), ')', '') LIKE '%{$escapedPhone}%'";

            $result = $this->db->query($sql);
            $totalCount += (int)$result->row['total'];
        }

        return $totalCount;
    }

    /**
     * Брои частичните съвпадения
     */
    private function getPartialMatchesCount($words, $hasExactMatches = false) {
        $language_id = $this->getLanguageId();

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(c.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(c.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(c.email) LIKE '%{$escapedWord}%'",
                "LOWER(c.telephone) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(c.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Ако имаме точни съвпадения, изключваме ги от частичните
        $excludeExact = '';
        if ($hasExactMatches) {
            if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
                $escapedEmail = $this->db->escape(mb_strtolower($words[0]));
                $excludeExact .= " AND LOWER(c.email) != '{$escapedEmail}'";
            }
            if (count($words) == 1 && !filter_var($words[0], FILTER_VALIDATE_EMAIL) && (strpos($words[0], '@') !== false || strpos($words[0], '.') !== false || strlen($words[0]) >= 3)) {
                $escapedEmailPart = $this->db->escape(mb_strtolower($words[0]));
                $excludeExact .= " AND LOWER(c.email) NOT LIKE '%{$escapedEmailPart}%'";
            }
            if (count($words) == 1 && preg_match('/^[\d\s\+\-\(\)]+$/', $words[0])) {
                $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $words[0]);
                $escapedPhone = $this->db->escape($cleanPhone);
                $excludeExact .= " AND REPLACE(REPLACE(REPLACE(REPLACE(c.telephone, ' ', ''), '-', ''), '(', ''), ')', '') NOT LIKE '%{$escapedPhone}%'";
            }
        }

        $sql = "
            SELECT COUNT(DISTINCT c.customer_id) as total
            FROM " . DB_PREFIX . "customer c
            LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
            WHERE cg.language_id = '" . (int)$language_id . "'
            AND (c.firstname IS NOT NULL AND c.firstname != ''
                 OR c.lastname IS NOT NULL AND c.lastname != '')
            AND ({$whereCondition})
            {$excludeExact}";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Рендира визуализацията на списъка с клиенти
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0, $extendedSearch = 0) {


        F()->log->developer($results, __FILE__, __LINE__);

        // Подготвяме данните за клиентите
        $customers = [];
        foreach ($results as $customer) {
            $customers[] = [
                'customer_id' => $customer['customer_id'],
                'name' => trim(($customer['firstname'] ?? '') . ' ' . ($customer['lastname'] ?? '')),
                'firstname' => $customer['firstname'] ?? '',
                'lastname' => $customer['lastname'] ?? '',
                'email' => $customer['email'] ?? '',
                'telephone' => $customer['telephone'] ?? '',
                'date_added' => $customer['date_added'] ?? '',
                'order_count' => $customer['order_count'] ?? 0,
                'total_spent' => $customer['total_spent'] ?? 0,
                // 'status' => ($customer['status'] ?? 0) == 1 ? 'Активен' : 'Неактивен',
                'status' => $customer['status'] ?? 'Неактивен',
                'status_class' => ($customer['status'] ?? 'Неактивен') == 'Активен' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
                // 'view' => $this->getAdminLink('sale/customer/info', '&customer_id=' . $customer['customer_id']),
                'edit' => $this->getAdminLink('sale/customer/edit', '&customer_id=' . $customer['customer_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'customers' => $customers,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/customers', '&query=' . urlencode($query) . '&extended_search=' . $extendedSearch . '&page={page}');
            $pagination->setProductText('клиента');
            $data['pagination'] = $pagination->render();
        }

        $this->setData($data);

        // Рендираме шаблона и връщаме HTML
        return $this->renderPartTemplate('common/global_search_customers');
    }

    /**
     * Получава CSS клас за статуса на клиента
     *
     * @param int $status Статус на клиента (0 или 1)
     * @return string CSS клас за статуса
     */
    private function getCustomerStatusClass($status) {
        return $status ? 'status-active' : 'status-inactive';
    }

    /**
     * Форматира валута за показване
     *
     * @param float $amount Сума
     * @param string $currency_code Код на валутата
     * @param float $currency_value Стойност на валутата
     * @return string Форматирана сума с валута
     */
    private function formatCurrency($amount, $currency_code, $currency_value) {
        if (!isset($this->currency)) {
            return number_format((float)$amount, 2, '.', '') . ' лв.';
        }

        return $this->currency->format($amount, $currency_code, $currency_value);
    }
}
